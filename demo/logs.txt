
---------------------------------------------------------------------
Camera calibration
On Thursday 19. June 2025, 13:35:49
Calibration directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/calibration
---------------------------------------------------------------------

Converting /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/calibration/Calib.qca.txt to .toml calibration file...

--> Residual (RMS) calibration errors for each camera are respectively [0.221, 0.235, 0.171, 0.191] px, 
which corresponds to [0.402, 0.445, 0.45, 0.505] mm.

Calibration file is stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/calibration/Calib_qualisys.toml.

Calibration took 0.01 seconds.


---------------------------------------------------------------------
Pose estimation for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:35:49
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------

Inference run only every 4 frames. Inbetween, pose estimation tracks previously detected points.

Estimating pose...
Using HALPE_26 model (body and feet) for pose estimation.

No valid CUDA installation found: using OpenVINO backend with CPU.

Pose tracking set up for "Body_with_feet" model.
Mode: balanced.
Tracking is performed with sports2d.

Found video files with mp4 extension.
--> Output video saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/pose/cam01_pose.mp4.
--> Output video saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/pose/cam02_pose.mp4.
--> Output video saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/pose/cam03_pose.mp4.
--> Output video saved to /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/pose/cam04_pose.mp4.

Pose estimation took 00h00m31s.


---------------------------------------------------------------------
Camera synchronization for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:36:20
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------

Synchronization is calculated on the whole sequence. This may take a while.
All keypoints are used to compute the best synchronization offset.
These keypoints are filtered with a Butterworth filter (cut-off frequency: 6 Hz, order: 4).
They are removed when their likelihood is below 0.4.

Synchronizing...

--> Camera cam01 and cam02: 0 frames offset, correlation 0.85.
--> Camera cam01 and cam03: -2 frames offset, correlation 0.69.
--> Camera cam01 and cam04: -1 frames offset, correlation 0.66.
Saving synchronized json files to the pose-sync folder.
Synchronized json files saved in /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/pose-sync.

Synchronization took 00h00m00s.


---------------------------------------------------------------------
Associating persons for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:36:21
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------


Single-person analysis selected.

--> Mean reprojection error for Neck point on all frames is 10.0 px, which roughly corresponds to 18.2 mm. 
--> In average, 0.05 cameras had to be excluded to reach the demanded 20 px error threshold after excluding points with likelihood below 0.3.

Tracked json files are stored in /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/pose-associated.

Associating persons took 00h00m00s.


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:37:58
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------

Trimming Participant 0 around frames (1, 98).

Mean reprojection error for Hip is 11.2 px (~ 0.02 m), reached with 0.54 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHip is 9.8 px (~ 0.018 m), reached with 1.0 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RKnee is 8.9 px (~ 0.016 m), reached with 0.59 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RAnkle is 10.2 px (~ 0.019 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RBigToe is 10.6 px (~ 0.019 m), reached with 0.33 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RSmallToe is 10.8 px (~ 0.02 m), reached with 0.15 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHeel is 9.1 px (~ 0.017 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHip is 11.0 px (~ 0.02 m), reached with 0.59 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LKnee is 11.5 px (~ 0.021 m), reached with 0.3 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LAnkle is 11.4 px (~ 0.021 m), reached with 0.04 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LBigToe is 10.6 px (~ 0.019 m), reached with 0.25 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LSmallToe is 12.2 px (~ 0.022 m), reached with 0.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHeel is 10.5 px (~ 0.019 m), reached with 0.06 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Neck is 9.9 px (~ 0.018 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Head is 11.6 px (~ 0.021 m), reached with 0.55 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Nose is 10.8 px (~ 0.02 m), reached with 0.64 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RShoulder is 10.4 px (~ 0.019 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RElbow is 9.6 px (~ 0.017 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RWrist is 9.5 px (~ 0.017 m), reached with 0.21 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LShoulder is 11.6 px (~ 0.021 m), reached with 0.12 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LElbow is 11.2 px (~ 0.02 m), reached with 0.1 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LWrist is 10.4 px (~ 0.019 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.

--> Mean reprojection error for all points on frames 1 to 98 is 10.6 px, which roughly corresponds to 19.3 mm. 
Cameras were excluded if likelihood was below 0.3 and if the reprojection error was above 15 px.
Gaps were interpolated with linear method if smaller than 10 frames. Larger gaps were filled with the last valid value.
In average, 0.26 cameras had to be excluded to reach these thresholds.
Camera cam03 was excluded 18% of the time, Camera cam04: 5%, Camera cam01: 3%, and Camera cam02: 1%.

3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/pose-3d/Demo_SinglePerson_1-97.trc.



All trc files have been converted to c3d.
Limb swapping was not handled.
Lens distortions were not taken into account.

Triangulation took 00h00m01s.


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:41:21
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------

Trimming Participant 0 around frames (1, 98).

Mean reprojection error for Hip is 11.2 px (~ 0.02 m), reached with 0.54 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHip is 9.8 px (~ 0.018 m), reached with 1.0 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RKnee is 8.9 px (~ 0.016 m), reached with 0.59 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RAnkle is 10.2 px (~ 0.019 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RBigToe is 10.6 px (~ 0.019 m), reached with 0.33 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RSmallToe is 10.8 px (~ 0.02 m), reached with 0.15 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHeel is 9.1 px (~ 0.017 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHip is 11.0 px (~ 0.02 m), reached with 0.59 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LKnee is 11.5 px (~ 0.021 m), reached with 0.3 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LAnkle is 11.4 px (~ 0.021 m), reached with 0.04 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LBigToe is 10.6 px (~ 0.019 m), reached with 0.25 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LSmallToe is 12.2 px (~ 0.022 m), reached with 0.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHeel is 10.5 px (~ 0.019 m), reached with 0.06 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Neck is 9.9 px (~ 0.018 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Head is 11.6 px (~ 0.021 m), reached with 0.55 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Nose is 10.8 px (~ 0.02 m), reached with 0.64 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RShoulder is 10.4 px (~ 0.019 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RElbow is 9.6 px (~ 0.017 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RWrist is 9.5 px (~ 0.017 m), reached with 0.21 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LShoulder is 11.6 px (~ 0.021 m), reached with 0.12 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LElbow is 11.2 px (~ 0.02 m), reached with 0.1 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LWrist is 10.4 px (~ 0.019 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.

--> Mean reprojection error for all points on frames 1 to 98 is 10.6 px, which roughly corresponds to 19.3 mm. 
Cameras were excluded if likelihood was below 0.3 and if the reprojection error was above 15 px.
Gaps were interpolated with linear method if smaller than 10 frames. Larger gaps were filled with the last valid value.
In average, 0.26 cameras had to be excluded to reach these thresholds.
Camera cam03 was excluded 18% of the time, Camera cam04: 5%, Camera cam01: 3%, and Camera cam02: 1%.

3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/pose-3d/Demo_SinglePerson_1-97.trc.



All trc files have been converted to c3d.
Limb swapping was not handled.
Lens distortions were not taken into account.

Triangulation took 00h00m00s.


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:42:31
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------

Trimming Participant 0 around frames (1, 98).

Mean reprojection error for Hip is 11.2 px (~ 0.02 m), reached with 0.54 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHip is 9.8 px (~ 0.018 m), reached with 1.0 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RKnee is 8.9 px (~ 0.016 m), reached with 0.59 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RAnkle is 10.2 px (~ 0.019 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RBigToe is 10.6 px (~ 0.019 m), reached with 0.33 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RSmallToe is 10.8 px (~ 0.02 m), reached with 0.15 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHeel is 9.1 px (~ 0.017 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHip is 11.0 px (~ 0.02 m), reached with 0.59 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LKnee is 11.5 px (~ 0.021 m), reached with 0.3 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LAnkle is 11.4 px (~ 0.021 m), reached with 0.04 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LBigToe is 10.6 px (~ 0.019 m), reached with 0.25 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LSmallToe is 12.2 px (~ 0.022 m), reached with 0.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHeel is 10.5 px (~ 0.019 m), reached with 0.06 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Neck is 9.9 px (~ 0.018 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Head is 11.6 px (~ 0.021 m), reached with 0.55 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Nose is 10.8 px (~ 0.02 m), reached with 0.64 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RShoulder is 10.4 px (~ 0.019 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RElbow is 9.6 px (~ 0.017 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RWrist is 9.5 px (~ 0.017 m), reached with 0.21 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LShoulder is 11.6 px (~ 0.021 m), reached with 0.12 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LElbow is 11.2 px (~ 0.02 m), reached with 0.1 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LWrist is 10.4 px (~ 0.019 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.

--> Mean reprojection error for all points on frames 1 to 98 is 10.6 px, which roughly corresponds to 19.3 mm. 
Cameras were excluded if likelihood was below 0.3 and if the reprojection error was above 15 px.
Gaps were interpolated with linear method if smaller than 10 frames. Larger gaps were filled with the last valid value.
In average, 0.26 cameras had to be excluded to reach these thresholds.
Camera cam03 was excluded 18% of the time, Camera cam04: 5%, Camera cam01: 3%, and Camera cam02: 1%.

3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/pose-3d/Demo_SinglePerson_1-97.trc.



All trc files have been converted to c3d.
Limb swapping was not handled.
Lens distortions were not taken into account.

Triangulation took 00h00m01s.


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:47:03
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:48:28
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:48:50
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:53:04
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:53:25
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:54:17
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:55:00
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:56:06
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------

Trimming Participant 0 around frames (1, 98).

Mean reprojection error for Hip is 11.2 px (~ 0.02 m), reached with 0.54 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHip is 9.8 px (~ 0.018 m), reached with 1.0 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RKnee is 8.9 px (~ 0.016 m), reached with 0.59 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RAnkle is 10.2 px (~ 0.019 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RBigToe is 10.6 px (~ 0.019 m), reached with 0.33 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RSmallToe is 10.8 px (~ 0.02 m), reached with 0.15 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHeel is 9.1 px (~ 0.017 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHip is 11.0 px (~ 0.02 m), reached with 0.59 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LKnee is 11.5 px (~ 0.021 m), reached with 0.3 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LAnkle is 11.4 px (~ 0.021 m), reached with 0.04 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LBigToe is 10.6 px (~ 0.019 m), reached with 0.25 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LSmallToe is 12.2 px (~ 0.022 m), reached with 0.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHeel is 10.5 px (~ 0.019 m), reached with 0.06 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Neck is 9.9 px (~ 0.018 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Head is 11.6 px (~ 0.021 m), reached with 0.55 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Nose is 10.8 px (~ 0.02 m), reached with 0.64 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RShoulder is 10.4 px (~ 0.019 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RElbow is 9.6 px (~ 0.017 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RWrist is 9.5 px (~ 0.017 m), reached with 0.21 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LShoulder is 11.6 px (~ 0.021 m), reached with 0.12 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LElbow is 11.2 px (~ 0.02 m), reached with 0.1 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LWrist is 10.4 px (~ 0.019 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.

--> Mean reprojection error for all points on frames 1 to 98 is 10.6 px, which roughly corresponds to 19.3 mm. 
Cameras were excluded if likelihood was below 0.3 and if the reprojection error was above 15 px.
Gaps were interpolated with linear method if smaller than 10 frames. Larger gaps were filled with the last valid value.
In average, 0.26 cameras had to be excluded to reach these thresholds.
Camera cam03 was excluded 18% of the time, Camera cam04: 5%, Camera cam01: 3%, and Camera cam02: 1%.

3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson/pose-3d/Demo_SinglePerson_1-97.trc.



All trc files have been converted to c3d.
Limb swapping was not handled.
Lens distortions were not taken into account.

Triangulation took 00h00m01s.


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 13:57:14
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 14:00:21
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 14:00:34
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 14:01:24
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------


---------------------------------------------------------------------
Triangulation of 2D points for Demo_SinglePerson, for all frames.
On Thursday 19. June 2025, 14:04:00
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/pose2sim/Pose2Sim/Demo_SinglePerson
---------------------------------------------------------------------


---------------------------------------------------------------------
Triangulation of 2D points for demo, for all frames.
On Thursday 19. June 2025, 14:58:23
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo
---------------------------------------------------------------------


---------------------------------------------------------------------
Triangulation of 2D points for demo, for all frames.
On Thursday 19. June 2025, 14:59:03
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo
---------------------------------------------------------------------

Trimming Participant 0 around frames (1, 98).

---------------------------------------------------------------------
Triangulation of 2D points for demo, for all frames.
On Thursday 19. June 2025, 14:59:36
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo
---------------------------------------------------------------------

Trimming Participant 0 around frames (1, 98).

Mean reprojection error for Hip is 9.4 px (~ 0.017 m), reached with 0.02 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHip is 12.6 px (~ 0.023 m), reached with 0.28 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RKnee is 8.8 px (~ 0.016 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RAnkle is 5.4 px (~ 0.01 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RBigToe is 9.8 px (~ 0.018 m), reached with 0.21 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RSmallToe is 10.3 px (~ 0.019 m), reached with 0.1 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHeel is 7.7 px (~ 0.014 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHip is 9.3 px (~ 0.017 m), reached with 0.1 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LKnee is 7.7 px (~ 0.014 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LAnkle is 3.9 px (~ 0.007 m), reached with 0.02 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LBigToe is 8.5 px (~ 0.016 m), reached with 0.07 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LSmallToe is 7.7 px (~ 0.014 m), reached with 0.06 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHeel is 5.3 px (~ 0.01 m), reached with 0.02 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Neck is 11.3 px (~ 0.021 m), reached with 0.06 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Head is 12.4 px (~ 0.023 m), reached with 0.48 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Nose is 10.3 px (~ 0.019 m), reached with 1.08 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RShoulder is 8.7 px (~ 0.016 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RElbow is 9.4 px (~ 0.017 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RWrist is 9.7 px (~ 0.018 m), reached with 0.31 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LShoulder is 8.9 px (~ 0.016 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LElbow is 9.1 px (~ 0.017 m), reached with 0.09 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LWrist is 7.6 px (~ 0.014 m), reached with 0.04 excluded cameras. 
  No frames needed to be interpolated.

--> Mean reprojection error for all points on frames 1 to 98 is 8.8 px, which roughly corresponds to 16.3 mm. 
Cameras were excluded if likelihood was below 0.3 and if the reprojection error was above 15 px.
Gaps were interpolated with linear method if smaller than 10 frames. Larger gaps were filled with the last valid value.
In average, 0.14 cameras had to be excluded to reach these thresholds.
Camera int_cam03_img was excluded 9% of the time, Camera int_cam04_img: 3%, Camera int_cam01_img: 1%, and Camera int_cam02_img: 1%.

3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo/pose-3d/demo_1-97.trc.



All trc files have been converted to c3d.
Limb swapping was not handled.
Lens distortions were not taken into account.

Triangulation took 00h00m00s.


---------------------------------------------------------------------
Filtering 3D coordinates for demo, for all frames.
On Thursday 19. June 2025, 14:59:37
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo
---------------------------------------------------------------------

--> Filter type: Butterworth low-pass. Order 4, Cut-off frequency 6 Hz.
Filtered 3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo/pose-3d/Demo_SinglePerson_1-97_filt_butterworth.trc.

All filtered trc files have been converted to c3d.
--> Filter type: Butterworth low-pass. Order 4, Cut-off frequency 6 Hz.
Filtered 3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo/pose-3d/demo_1-97_filt_butterworth.trc.

All filtered trc files have been converted to c3d.



---------------------------------------------------------------------
Triangulation of 2D points for demo, for all frames.
On Thursday 19. June 2025, 15:10:55
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo
---------------------------------------------------------------------

Trimming Participant 0 around frames (1, 98).

Mean reprojection error for Hip is 9.4 px (~ 0.017 m), reached with 0.02 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHip is 12.6 px (~ 0.023 m), reached with 0.28 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RKnee is 8.8 px (~ 0.016 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RAnkle is 5.4 px (~ 0.01 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RBigToe is 9.8 px (~ 0.018 m), reached with 0.21 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RSmallToe is 10.3 px (~ 0.019 m), reached with 0.1 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHeel is 7.7 px (~ 0.014 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHip is 9.3 px (~ 0.017 m), reached with 0.1 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LKnee is 7.7 px (~ 0.014 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LAnkle is 3.9 px (~ 0.007 m), reached with 0.02 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LBigToe is 8.5 px (~ 0.016 m), reached with 0.07 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LSmallToe is 7.7 px (~ 0.014 m), reached with 0.06 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHeel is 5.3 px (~ 0.01 m), reached with 0.02 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Neck is 11.3 px (~ 0.021 m), reached with 0.06 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Head is 12.4 px (~ 0.023 m), reached with 0.48 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Nose is 10.3 px (~ 0.019 m), reached with 1.08 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RShoulder is 8.7 px (~ 0.016 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RElbow is 9.4 px (~ 0.017 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RWrist is 9.7 px (~ 0.018 m), reached with 0.31 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LShoulder is 8.9 px (~ 0.016 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LElbow is 9.1 px (~ 0.017 m), reached with 0.09 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LWrist is 7.6 px (~ 0.014 m), reached with 0.04 excluded cameras. 
  No frames needed to be interpolated.

--> Mean reprojection error for all points on frames 1 to 98 is 8.8 px, which roughly corresponds to 16.3 mm. 
Cameras were excluded if likelihood was below 0.3 and if the reprojection error was above 15 px.
Gaps were interpolated with linear method if smaller than 10 frames. Larger gaps were filled with the last valid value.
In average, 0.14 cameras had to be excluded to reach these thresholds.
Camera int_cam03_img was excluded 9% of the time, Camera int_cam04_img: 3%, Camera int_cam01_img: 1%, and Camera int_cam02_img: 1%.

3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo/pose-3d/demo_1-97.trc.



All trc files have been converted to c3d.
Limb swapping was not handled.
Lens distortions were not taken into account.

Triangulation took 00h00m00s.


---------------------------------------------------------------------
Filtering 3D coordinates for demo, for all frames.
On Thursday 19. June 2025, 15:10:56
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo
---------------------------------------------------------------------

--> Filter type: Butterworth low-pass. Order 4, Cut-off frequency 6 Hz.
Filtered 3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo/pose-3d/Demo_SinglePerson_1-97_filt_butterworth.trc.

All filtered trc files have been converted to c3d.
--> Filter type: Butterworth low-pass. Order 4, Cut-off frequency 6 Hz.
Filtered 3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo/pose-3d/demo_1-97_filt_butterworth.trc.

All filtered trc files have been converted to c3d.

Filtering took 00h00m00s.


---------------------------------------------------------------------
Triangulation of 2D points for demo, for all frames.
On Thursday 19. June 2025, 15:28:57
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo
---------------------------------------------------------------------

Trimming Participant 0 around frames (1, 98).

Mean reprojection error for Hip is 11.2 px (~ 0.02 m), reached with 0.54 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHip is 9.8 px (~ 0.018 m), reached with 1.0 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RKnee is 8.9 px (~ 0.016 m), reached with 0.59 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RAnkle is 10.2 px (~ 0.019 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RBigToe is 10.6 px (~ 0.019 m), reached with 0.33 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RSmallToe is 10.8 px (~ 0.02 m), reached with 0.15 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHeel is 9.1 px (~ 0.017 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHip is 11.0 px (~ 0.02 m), reached with 0.59 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LKnee is 11.5 px (~ 0.021 m), reached with 0.3 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LAnkle is 11.4 px (~ 0.021 m), reached with 0.04 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LBigToe is 10.6 px (~ 0.019 m), reached with 0.25 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LSmallToe is 12.2 px (~ 0.022 m), reached with 0.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHeel is 10.5 px (~ 0.019 m), reached with 0.06 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Neck is 9.9 px (~ 0.018 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Head is 11.6 px (~ 0.021 m), reached with 0.55 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Nose is 10.8 px (~ 0.02 m), reached with 0.64 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RShoulder is 10.4 px (~ 0.019 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RElbow is 9.6 px (~ 0.017 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RWrist is 9.5 px (~ 0.017 m), reached with 0.21 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LShoulder is 11.6 px (~ 0.021 m), reached with 0.12 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LElbow is 11.2 px (~ 0.02 m), reached with 0.1 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LWrist is 10.4 px (~ 0.019 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.

--> Mean reprojection error for all points on frames 1 to 98 is 10.6 px, which roughly corresponds to 19.3 mm. 
Cameras were excluded if likelihood was below 0.3 and if the reprojection error was above 15 px.
Gaps were interpolated with linear method if smaller than 10 frames. Larger gaps were filled with the last valid value.
In average, 0.26 cameras had to be excluded to reach these thresholds.
Camera cam03 was excluded 18% of the time, Camera cam04: 5%, Camera cam01: 3%, and Camera cam02: 1%.

3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo/pose-3d/demo_1-97.trc.



All trc files have been converted to c3d.
Limb swapping was not handled.
Lens distortions were not taken into account.

Triangulation took 00h00m01s.


---------------------------------------------------------------------
Filtering 3D coordinates for demo, for all frames.
On Thursday 19. June 2025, 15:28:58
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo
---------------------------------------------------------------------

--> Filter type: Butterworth low-pass. Order 4, Cut-off frequency 6 Hz.
Filtered 3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo/pose-3d/Demo_SinglePerson_1-97_filt_butterworth.trc.

All filtered trc files have been converted to c3d.
--> Filter type: Butterworth low-pass. Order 4, Cut-off frequency 6 Hz.
Filtered 3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo/pose-3d/demo_1-97_filt_butterworth.trc.

All filtered trc files have been converted to c3d.

Filtering took 00h00m00s.


---------------------------------------------------------------------
Triangulation of 2D points for demo, for all frames.
On Thursday 19. June 2025, 15:39:57
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo
---------------------------------------------------------------------

Trimming Participant 0 around frames (1, 98).

Mean reprojection error for Hip is 11.2 px (~ 0.02 m), reached with 0.54 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHip is 9.8 px (~ 0.018 m), reached with 1.0 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RKnee is 8.9 px (~ 0.016 m), reached with 0.59 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RAnkle is 10.2 px (~ 0.019 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RBigToe is 10.6 px (~ 0.019 m), reached with 0.33 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RSmallToe is 10.8 px (~ 0.02 m), reached with 0.15 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RHeel is 9.1 px (~ 0.017 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHip is 11.0 px (~ 0.02 m), reached with 0.59 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LKnee is 11.5 px (~ 0.021 m), reached with 0.3 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LAnkle is 11.4 px (~ 0.021 m), reached with 0.04 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LBigToe is 10.6 px (~ 0.019 m), reached with 0.25 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LSmallToe is 12.2 px (~ 0.022 m), reached with 0.19 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LHeel is 10.5 px (~ 0.019 m), reached with 0.06 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Neck is 9.9 px (~ 0.018 m), reached with 0.01 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Head is 11.6 px (~ 0.021 m), reached with 0.55 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for Nose is 10.8 px (~ 0.02 m), reached with 0.64 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RShoulder is 10.4 px (~ 0.019 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RElbow is 9.6 px (~ 0.017 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for RWrist is 9.5 px (~ 0.017 m), reached with 0.21 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LShoulder is 11.6 px (~ 0.021 m), reached with 0.12 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LElbow is 11.2 px (~ 0.02 m), reached with 0.1 excluded cameras. 
  No frames needed to be interpolated.
Mean reprojection error for LWrist is 10.4 px (~ 0.019 m), reached with 0.03 excluded cameras. 
  No frames needed to be interpolated.

--> Mean reprojection error for all points on frames 1 to 98 is 10.6 px, which roughly corresponds to 19.3 mm. 
Cameras were excluded if likelihood was below 0.3 and if the reprojection error was above 15 px.
Gaps were interpolated with linear method if smaller than 10 frames. Larger gaps were filled with the last valid value.
In average, 0.26 cameras had to be excluded to reach these thresholds.
Camera cam03 was excluded 18% of the time, Camera cam04: 5%, Camera cam01: 3%, and Camera cam02: 1%.

3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo/pose-3d/demo_1-97.trc.



All trc files have been converted to c3d.
Limb swapping was not handled.
Lens distortions were not taken into account.

Triangulation took 00h00m00s.


---------------------------------------------------------------------
Filtering 3D coordinates for demo, for all frames.
On Thursday 19. June 2025, 15:39:58
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo
---------------------------------------------------------------------

--> Filter type: Butterworth low-pass. Order 4, Cut-off frequency 6 Hz.
Filtered 3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo/pose-3d/Demo_SinglePerson_1-97_filt_butterworth.trc.

All filtered trc files have been converted to c3d.
--> Filter type: Butterworth low-pass. Order 4, Cut-off frequency 6 Hz.
Filtered 3D coordinates are stored at /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo/pose-3d/demo_1-97_filt_butterworth.trc.

All filtered trc files have been converted to c3d.

Filtering took 00h00m00s.


---------------------------------------------------------------------
Triangulation of 2D points for demo, for all frames.
On Thursday 19. June 2025, 16:05:09
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo
---------------------------------------------------------------------


---------------------------------------------------------------------
Triangulation of 2D points for demo, for all frames.
On Thursday 19. June 2025, 16:34:20
Project directory: /home/<USER>/DELTAX_PROJECTS/PROJECTS/4_Gaze_Tracking/Pose2Sim/demo
---------------------------------------------------------------------

