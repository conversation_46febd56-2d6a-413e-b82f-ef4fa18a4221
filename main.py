'''
###########################################################################
POSE2SIM - TRIANGULATION AND FILTERING
###########################################################################
'''

import os
import toml
import time
import logging
from datetime import datetime


def setup_logging(project_dir):
    '''Create simple logging setup'''
    logging.basicConfig(
        format='%(message)s',
        level=logging.INFO,
        handlers=[
            logging.FileHandler(os.path.join(project_dir, 'logs.txt')),
            logging.StreamHandler()
        ]
    )


def load_config(config_path):
    '''Load configuration from TOML file'''
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Config file not found: {config_path}")

    config_dict = toml.load(config_path)

    # Set project directory to the directory containing the config file
    project_dir = os.path.dirname(os.path.abspath(config_path))
    config_dict.setdefault('project', {})['project_dir'] = project_dir

    return config_dict


class Pose2SimPipeline:
    def __init__(self, config_path):
        self.config_dict = load_config(config_path)
        self.project_dir = self.config_dict['project']['project_dir']

        # Setup logging
        use_custom_logging = self.config_dict.get('logging', {}).get('use_custom_logging', False)
        if not use_custom_logging:
            setup_logging(self.project_dir)

    def _log_step_header(self, step_name):
        seq_name = os.path.basename(self.project_dir)
        frame_range = self.config_dict.get('project', {}).get('frame_range')
        frames = "all frames" if not frame_range or frame_range in ('all','auto') else f"frames {frame_range[0]} to {frame_range[1]}"
        logging.info("\n---------------------------------------------------------------------")
        logging.info(f"{step_name} for {seq_name}, for {frames}.")
        logging.info(f"On {datetime.now().strftime('%A %d. %B %Y, %H:%M:%S')}")
        logging.info(f"Project directory: {self.project_dir}")
        logging.info("---------------------------------------------------------------------\n")

    def triangulation(self):
        from scripts.triangulation import triangulate_all
        self._log_step_header("Triangulation of 2D points")
        start = time.time()
        triangulate_all(self.config_dict)
        elapsed = time.time() - start
        logging.info(f'\nTriangulation took {time.strftime("%Hh%Mm%Ss", time.gmtime(elapsed))}.\n')

    def filtering(self):
        from scripts.filtering import filter_all
        self._log_step_header("Filtering 3D coordinates")
        start = time.time()
        filter_all(self.config_dict)
        elapsed = time.time() - start
        logging.info(f'\nFiltering took {time.strftime("%Hh%Mm%Ss", time.gmtime(elapsed))}.\n')

    def run_all(self, do_triangulation=True, do_filtering=True):
        logging.info("\n\n=====================================================================")
        logging.info(f"RUNNING TRIANGULATION AND FILTERING.")
        logging.info(f"On {datetime.now().strftime('%A %d. %B %Y, %H:%M:%S')}")
        logging.info(f"Project directory: {self.project_dir}\n")
        logging.info("=====================================================================\n")

        overall_start = time.time()

        if do_triangulation:
            logging.info("\n\n=====================================================================")
            logging.info('Running triangulation...')
            logging.info("=====================================================================")
            self.triangulation()

        if do_filtering:
            logging.info("\n\n=====================================================================")
            logging.info('Running filtering...')
            logging.info("=====================================================================")
            self.filtering()

        logging.info("Pose2Sim triangulation and filtering completed.")
        overall_elapsed = time.time() - overall_start
        logging.info(f'\nTotal processing time: {time.strftime("%Hh%Mm%Ss", time.gmtime(overall_elapsed))}.\n')


def triangulation(config_path):
    pipeline = Pose2SimPipeline(config_path)
    pipeline.triangulation()


def filtering(config_path):
    pipeline = Pose2SimPipeline(config_path)
    pipeline.filtering()


def run_all(config_path, **kwargs):
    pipeline = Pose2SimPipeline(config_path)
    pipeline.run_all(**kwargs)


def main():
    config_path = "demo/Config.toml"

    if not os.path.exists(config_path):
        print(f"Config file not found: {config_path}")
        return

    print("Running triangulation...")
    triangulation(config_path)

    print("Running filtering...")
    filtering(config_path)


if __name__ == '__main__':
    main()
